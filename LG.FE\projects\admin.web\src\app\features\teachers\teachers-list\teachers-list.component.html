<!-- teachers-list.component.html -->
<div class="teachers-page-container">
  <!-- Main Content (Table & Applied Filters) -->

  <div class="main-content">
    <!-- Applied Filters Section -->
    <div class="applied-filters-container surface-card p-3 border-round mb-3" (click)="$event.stopPropagation()">
      <div class="applied-filters-header flex justify-content-between align-items-center mb-3">
        <div class="flex align-items-center gap-2">
          <i class="pi pi-filter text-primary font-bold"></i>
          <h5 class="m-0 text-900 font-medium font-bold">Applied Filters :</h5>
          <div class="applied-filters-tags flex flex-wrap gap-2">
            
            <!-- Sort Column -->
            <div class="filter-chip flex align-items-center gap-2 surface-100 border-round-2xl px-3 py-2"
              *ngIf="hasSortFilterInUrl()">
              <i class="pi pi-sort-alt text-600 text-sm"></i>
              <span class="text-900 font-medium">Sort: {{ getSortColumnDisplayName() }}
                ({{ queryParams().sortDirection === 'asc' ? 'Ascending' : 'Descending' }})
              </span>
              <button pButton class="p-button-rounded p-button-text p-button-sm" icon="pi pi-times"
                (click)="removeFilter('sortColumn', $event)">
              </button>
            </div>

            <!-- Search Term -->
            <div class="filter-chip flex align-items-center gap-2 surface-100 border-round-2xl px-3 py-2"
              *ngIf="hasFilterInUrl(getTeachersRequestFieldNames.searchTerm!)">
              <i class="pi pi-search text-600 text-sm"></i>
              <span class="text-900 font-medium">{{ currentUrlParams()[getTeachersRequestFieldNames.searchTerm!]
                }}</span>
              <button pButton class="p-button-rounded p-button-text p-button-sm" icon="pi pi-times"
                (click)="removeFilter(getTeachersRequestFieldNames.searchTerm!, $event)">
              </button>
            </div>

            <!-- Gender -->
            <div class="filter-chip flex align-items-center gap-2 surface-100 border-round-2xl px-3 py-2"
              *ngIf="hasFilterInUrl(getTeachersRequestFieldNames.gender)">
              <i class="pi pi-user text-600 text-sm"></i>
              <span class="text-900 font-medium applied-filter">Gender: <span class="font-bold">{{
                  getFilterLabelFromUrl('gender', getTeachersRequestFieldNames.gender) }}</span></span>
              <button pButton class="p-button-rounded p-button-text p-button-sm" icon="pi pi-times"
                (click)="removeFilter(getTeachersRequestFieldNames.gender, $event)">
              </button>
            </div>

            <!-- Teaching Language -->
            <div class="filter-chip flex align-items-center gap-2 surface-100 border-round-2xl px-3 py-2"
              *ngIf="hasFilterInUrl(getTeachersRequestFieldNames.teachingLanguage!)">
              <i class="pi pi-globe text-600 text-sm"></i>
              <span class="text-900 font-medium">Teaching: {{ getFilterLabelFromUrl('language',
                getTeachersRequestFieldNames.teachingLanguage!) }}</span>
              <button pButton class="p-button-rounded p-button-text p-button-sm" icon="pi pi-times"
                (click)="removeFilter(getTeachersRequestFieldNames.teachingLanguage!, $event)">
              </button>
            </div>

            <!-- Native Language -->
            <div class="filter-chip flex align-items-center gap-2 surface-100 border-round-2xl px-3 py-2"
              *ngIf="hasFilterInUrl(getTeachersRequestFieldNames.speakingLanguage!)">
              <i class="pi pi-flag text-600 text-sm"></i>
              <span class="text-900 font-medium">Native: {{getFilterLabelFromUrl('speakingLanguage',
                getTeachersRequestFieldNames.speakingLanguage!)}}</span>
              <button pButton class="p-button-rounded p-button-text p-button-sm" icon="pi pi-times"
                (click)="removeFilter(getTeachersRequestFieldNames.speakingLanguage!)">
              </button>
            </div>

            <!-- Show Only Active -->
            <div class="filter-chip flex align-items-center gap-2 surface-100 border-round-2xl px-3 py-2"
              *ngIf="hasFilterInUrl(getTeachersRequestFieldNames.includeBlocked!)">
              <i class="pi pi-check-circle text-600 text-sm"></i>
              <span class="text-900 font-medium">Include Blocked</span>
              <button pButton class="p-button-rounded p-button-text p-button-sm" icon="pi pi-times"
                (click)="removeFilter(getTeachersRequestFieldNames.includeBlocked!, $event)">
              </button>
            </div>

            <!-- Availability Status -->
            <div class="filter-chip flex align-items-center gap-2 surface-100 border-round-2xl px-3 py-2"
              *ngIf="hasFilterInUrl(getTeachersRequestFieldNames.availabilityStatus!)">
              <i class="pi pi-clock text-600 text-sm"></i>
              <span class="text-900 font-medium">Status: {{ getFilterLabelFromUrl('availabilityStatus',
                getTeachersRequestFieldNames.availabilityStatus!) }}</span>
              <button pButton class="p-button-rounded p-button-text p-button-sm" icon="pi pi-times"
                (click)="removeFilter(getTeachersRequestFieldNames.availabilityStatus!, $event)">
              </button>
            </div>

            <!-- Teaching Ages Experience -->
            <div class="filter-chip flex align-items-center gap-2 surface-100 border-round-2xl px-3 py-2"
              *ngIf="hasFilterInUrl(getTeachersRequestFieldNames.teachingAgesExperience!)">
              <i class="pi pi-briefcase text-600 text-sm"></i>
              <span class="text-900 font-medium">Experience: {{ getFilterLabelFromUrl('teachingAgesExperience',
                getTeachersRequestFieldNames.teachingAgesExperience!) }}</span>
              <button pButton class="p-button-rounded p-button-text p-button-sm" icon="pi pi-times"
                (click)="removeFilter(getTeachersRequestFieldNames.teachingAgesExperience!, $event)">
              </button>
            </div>

            <!-- Student Ages Preference -->
            <div class="filter-chip flex align-items-center gap-2 surface-100 border-round-2xl px-3 py-2"
              *ngIf="hasFilterInUrl(getTeachersRequestFieldNames.teacherStudentAgesPreference!)">
              <i class="pi pi-users text-600 text-sm"></i>
              <span class="text-900 font-medium">Preferred Ages: {{ getFilterLabelFromUrl('studentAgesPreference',
                getTeachersRequestFieldNames.teacherStudentAgesPreference!) }}</span>
              <button pButton class="p-button-rounded p-button-text p-button-sm" icon="pi pi-times"
                (click)="removeFilter(getTeachersRequestFieldNames.teacherStudentAgesPreference!, $event)">
              </button>
            </div>

            <!-- Registration Date Range -->
            <div class="filter-chip flex align-items-center gap-2 surface-100 border-round-2xl px-3 py-2"
              *ngIf="hasFilterInUrl(getTeachersRequestFieldNames.approvedDateFrom!) || hasFilterInUrl(getTeachersRequestFieldNames.approvedDateTo!)">
              <i class="pi pi-calendar text-600 text-sm"></i>
              <span class="text-900 font-medium">Approved: {{
                currentUrlParams()[getTeachersRequestFieldNames.approvedDateFrom!] | date: 'mediumDate' }}
                - {{ currentUrlParams()[getTeachersRequestFieldNames.approvedDateTo!] | date: 'mediumDate' }}</span>
              <button pButton class="p-button-rounded p-button-text p-button-sm" icon="pi pi-times"
                (click)="removeFilter('dateOfRegistration', $event)">
              </button>
            </div>

            <!-- Student Ages Range -->
            <div class="filter-chip flex align-items-center gap-2 surface-100 border-round-2xl px-3 py-2"
              *ngIf="hasRangeFilterInUrl(getTeachersRequestFieldNames.studentAgesMin, getTeachersRequestFieldNames.studentAgesMax, 2, 17)">
              <i class="pi pi-chart-bar text-600 text-sm"></i>
              <span class="text-900 font-medium">Ages: {{
                currentUrlParams()[getTeachersRequestFieldNames.studentAgesMin] || 2 }}
                - {{ currentUrlParams()[getTeachersRequestFieldNames.studentAgesMax] || 17 }}</span>
              <button pButton class="p-button-rounded p-button-text p-button-sm" icon="pi pi-times"
                (click)="removeFilter('studentAgesRange', $event)">
              </button>
            </div>

          </div>

        </div>
        <button pButton type="button" label="Clear All" icon="pi pi-filter-slash"
          class="p-button-outlined p-button-danger p-button-sm" (click)="resetFilters()">
        </button>
      </div>
    </div>


    <!-- Sidebar (Filters) -->
    <div class="sidebar">
      <div class="filters-container surface-card p-3 border-round">
        <div class="filter-header flex justify-content-between align-items-center">
          <h4 class="text-900 font-medium m-0 mb-3">
            <i class="pi pi-filter text-primary mr-2"></i>Filters
          </h4>
          <p-button pButton type="button" [icon]="isFilterOpen() ? 'pi pi-chevron-up' : 'pi pi-chevron-down'"
            class="p-button-rounded p-button-text p-button-sm filter-toggle-btn" (click)="toggleFilters()">
          </p-button>
        </div>

        <div id="filters-content-wrapper">
          <div id="filters-content">
            <div class="grid">
              <!-- Basic Filters -->
              <div class="col-12">
                <div class="flex flex-column gap-1">
                  <!-- Gender Dropdown -->
                  <div class="field">
                    <label for="gender" class="block text-sm mb-2">Gender</label>
                    <p-dropdown id="gender" [options]="this.enumDropdownOptionsService.genderOptions"
                      [ngModel]="queryParams().gender" (ngModelChange)="updateGender($event)"
                      placeholder="Select Gender" optionLabel="label" optionValue="value" class="w-full"></p-dropdown>
                  </div>
                </div>
              </div>

              <!-- Language Filters -->
              <div class="col-12">
                <div class="flex flex-column gap-1">
                  <!-- Teaching Language Dropdown -->
                  <div class="field">
                    <label for="teachingLanguage" class="block text-sm mb-2">Teaching Language</label>
                    <p-dropdown id="teachingLanguage" [options]="teachingLanguages()"
                      [ngModel]="queryParams().teachingLanguage" (ngModelChange)="updateTeachingLanguage($event)"
                      placeholder="Select Teaching Language" optionLabel="name" optionValue="id"
                      class="w-full"></p-dropdown>
                  </div>

                  <!-- Native Language Dropdown -->
                  <div class="field">
                    <label for="speakingLanguage" class="block text-sm mb-2">Speaking Language</label>
                    <p-dropdown id="speakingLanguage" [options]="nativeLanguages()"
                    [filter]="true"
                      [ngModel]="queryParams().speakingLanguage" (ngModelChange)="updateNativeLanguage($event)"
                      placeholder="Select Speaking Language" class="w-full"></p-dropdown>
                  </div>
                </div>
              </div>

              <!-- Status and Experience -->
              <div class="col-12">
                <div class="flex flex-column gap-1">
                  <!-- Availability Status MultiSelect -->
                  <div class="field">
                    <label for="availabilityStatus" class="block text-sm mb-2">Availability Status</label>
                    <p-multiSelect id="availabilityStatus" class="w-full"
                      [options]="this.enumDropdownOptionsService.availabilityStatusEnumFlagsOptions"
                      [ngModel]="selectedAvailabilityStatuses()"
                      (ngModelChange)="selectedAvailabilityStatuses.set($event)"
                      defaultLabel="Select Availability Statuses" optionLabel="label" optionValue="value"
                      display="chip"></p-multiSelect>
                  </div>

                  <!-- Show Only Active Toggle -->
                  <div class="field-checkbox">
                    <p-checkbox id="includeBlocked" [ngModel]="queryParams().includeBlocked!"
                      (ngModelChange)="updateShowOnlyActive($event)" [binary]="true"></p-checkbox>
                    <label for="includeBlocked" class="ml-2">Include Blocked {{queryParams().includeBlocked!}}</label>
                  </div>
                </div>
              </div>

              <!-- Teaching Experience -->
              <div class="col-12">
                <div class="flex flex-column gap-1">
                  <!-- Teaching Ages Experience MultiSelect -->
                  <div class="field">
                    <label for="teachingAgesExperience" class="block text-sm mb-2">Teaching Ages Experience</label>
                    <p-multiSelect id="teachingAgesExperience" class="w-full"
                      [options]="this.enumDropdownOptionsService.teachingAgesExperienceEnumFlagsOptions"
                      [ngModel]="selectedTeachingAgesExperience()"
                      (ngModelChange)="selectedTeachingAgesExperience.set($event)" defaultLabel="Select Experience"
                      optionLabel="label" optionValue="value" display="chip"></p-multiSelect>
                  </div>

                  <!-- Student Ages Preference MultiSelect -->
                  <div class="field">
                    <label for="teacherStudentAgesPreference" class="block text-sm mb-2">Student Ages Preference</label>
                    <p-multiSelect id="teacherStudentAgesPreference" class="w-full"
                      [options]="this.enumDropdownOptionsService.teacherStudentAgesPreferenceEnumFlagsOptions"
                      [ngModel]="selectedTeacherStudentAgesPreference()"
                      (ngModelChange)="selectedTeacherStudentAgesPreference.set($event)"
                      defaultLabel="Select Preferences" optionLabel="label" optionValue="value"
                      display="chip"></p-multiSelect>
                  </div>
                </div>
              </div>

              <!-- Date Range -->
              <div class="col-12">
                <div class="flex flex-column gap-1">
                  <label class="block text-sm mb-2">Registration Date Range</label>
                  <div class="flex gap-2">
                    <p-datepicker id="approvedDateFrom" [ngModel]="queryParams().approvedDateFrom"
                      (ngModelChange)="updateDateOfRegistrationFrom($event)" dateFormat="yy-mm-dd" placeholder="From"
                      [appendTo]="'body'" class="flex-1"></p-datepicker>
                    <p-datepicker id="approvedDateTo" [ngModel]="queryParams().approvedDateTo"
                      (ngModelChange)="updateDateOfRegistrationTo($event)" dateFormat="yy-mm-dd" placeholder="To"
                      [appendTo]="'body'" class="flex-1"></p-datepicker>
                  </div>
                </div>
              </div>

              <!-- Age Range Slider -->
              <div class="col-12">
                <div class="flex flex-column gap-1">
                  <div class="field p-2">
                    <label class="block text-sm mb-3">Student Ages Range ({{ queryParams().studentAgesMin }}
                      - {{ queryParams().studentAgesMax }})</label>
                    <div style="padding: 0 7px">
                      <p-slider [ngModel]="studentAgesRange()" (ngModelChange)="studentAgesRange.set($event)"
                        [range]="true" [min]="2" [max]="17" (onChange)="onStudentAgesRangeChange()" class="w-full">
                      </p-slider>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>


          <!-- Action Buttons -->
          <div class="col-12">
            <div class="flex gap-2 justify-content-end mt-4">
              <button pButton type="button" label="Reset" class="p-button-outlined" (click)="resetFilters()"></button>
              <button pButton type="button" label="Apply Filters" class="p-button-primary"
                (click)="onSearch()"></button>
            </div>
          </div>
        </div>

      </div>
    </div>

    <!-- Table Container -->
    <div class="table-container surface-card p-3 border-round">
      <p-table #dt [value]="teachersResponse().pageData" [lazy]="true" [paginator]="true"
        [rows]="queryParams().pageSize" [totalRecords]="totalRecords()" [loading]="isLoading()"
        [rowsPerPageOptions]="rowsPerPageOptions()" [showCurrentPageReport]="true"
        [sortField]="queryParams().sortColumn" [sortOrder]="queryParams().sortDirection === 'asc' ? 1 : -1"
        [reorderableColumns]="true" currentPageReportTemplate="Showing {first} to {last} of {totalRecords} teachers"
        (onPage)="onPageChange($event)" (onSort)="onSortChange($event)" [scrollable]="true" scrollHeight="flex"
        [resizableColumns]="true" showGridlines stripedRows
        [tableStyle]="{'min-width': '1000px; min-height: calc(100% + 300px)'}" [columns]="selectedColumns()">

        <ng-template pTemplate="caption">
          <div class="flex flex-column md:flex-row justify-content-between align-items-center p-0 gap-4">
            <div class="flex-1">
              <div class="field" style="width: 300px">
                <div class="search-input-container">
                  <input id="searchTerm" type="text" class="p-inputtext w-full" [value]="queryParams().searchTerm || ''"
                    (input)="updateSearchTerm($event)" placeholder="Search by name or email" />
                  <button *ngIf="queryParams().searchTerm" class="search-clear-button" type="button"
                    (click)="clearSearchTerm()">
                    <i class="pi pi-times"></i>
                  </button>
                </div>
              </div>
            </div>
            <div class="flex align-items-center gap-3">
              <p-multiSelect [options]="cols" [ngModel]="selectedColumns()" (ngModelChange)="onColumnsChange($event)"
                [filter]="true" optionLabel="header" placeholder="Choose Columns"
                [selectedItemsLabel]="'{0}/' + cols.length + ' columns shown'" scrollHeight="400px" />
              <p-button icon="pi pi-download" label="Export" severity="secondary" (click)="exportTable()"
                class="p-button-sm" />
            </div>
          </div>


        </ng-template>

        <ng-template pTemplate="header">

                    <tr>
            <th alignFrozen="left" pFrozenColumn style="width: 100px">Actions</th>

            <th *ngFor="let col of selectedColumns()" pReorderableColumn
              [pSortableColumn]="col.sortable ? col.field : undefined"
              [attr.style]="col.maxWidth !== undefined ? 'max-width:' + col.maxWidth + ' !important; width:max-content !important; ' : 'width:max-content !important;'">
              {{ col.header }}
              <p-sortIcon *ngIf="col.sortable" [field]="col.field"></p-sortIcon>
            </th>
          </tr>

          
        <!-- Table Header -->
        <app-data-grid-header-footer
          [columns]="selectedColumns()"
          [sortable]="true"
          [reorderable]="true"
          [showHeader]="true"
          [showFooter]="false"
          [showActionsColumn]="true"
          actionsColumnHeader="Actions"
          actionsColumnWidth="100px">
        </app-data-grid-header-footer>
        </ng-template>

        <!-- Table Body -->
        <ng-template pTemplate="body" let-teacher let-index="rowIndex">
          <tr [pReorderableRow]="index">

            <td alignFrozen="left" pFrozenColumn>
              <div class="flex justify-content-center">
                <button (click)="goToTeacherOverview(teacher.id)" pButton icon="pi pi-eye" class="p-button-text p-button-sm"></button>
              </div>
            </td>

            <td *ngFor="let col of selectedColumns()">
              <ng-container [ngSwitch]="col.field">
                <!-- Formatted dates -->
                <ng-container *ngSwitchCase="searchTeacherDtoFieldNames.approvedDate">
                  <div class="multiline"
                    [attr.style]="col.maxWidth !== undefined ? 'max-width:' + col.maxWidth + ' !important; width:max-content !important; ' : 'width:max-content !important;'">
                    {{ teacher.approvedDate | date:'shortDate' }}
                  </div>
                </ng-container>

                <!-- Countries -->
                <ng-container *ngSwitchCase="searchTeacherDtoFieldNames.country">
                  <div class="multiline"
                    [attr.style]="col.maxWidth !== undefined ? 'max-width:' + col.maxWidth + ' !important; width:max-content !important; ' : 'width:max-content !important;'">
                    {{ teacher[col.field] }}
                  </div>
                </ng-container>

                <!-- Gender -->
                <ng-container *ngSwitchCase="searchTeacherDtoFieldNames.gender">
                  <div class="multiline"
                    [attr.style]="col.maxWidth !== undefined ? 'max-width:' + col.maxWidth + ' !important; width:max-content !important; ' : 'width:max-content !important;'">
                    {{ teacher.gender === 1 ? 'Male' : 'Female' }}
                  </div>
                </ng-container>

                <!-- Languages lists -->
                <ng-container *ngSwitchCase="searchTeacherDtoFieldNames.speakingLanguages">
                  <div class="multiline"
                    [attr.style]="col.maxWidth !== undefined ? 'max-width:' + col.maxWidth + ' !important; width:max-content !important; ' : 'width:max-content !important;'">
                    <div class="language-list">
                      <span *ngFor="let language of teacher.speakingLanguages" class="language-tag">
                        {{ language.language }}
                      </span>
                    </div>
                  </div>
                </ng-container>

                <ng-container *ngSwitchCase="searchTeacherDtoFieldNames.teacherTeachingLanguages">
                  <div class="multiline"
                    [attr.style]="col.maxWidth !== undefined ? 'max-width:' + col.maxWidth + ' !important; width:max-content !important; ' : 'width:max-content !important;'">
                    <div class="language-list">
                      <span *ngFor="let language of teacher.teacherTeachingLanguages" class="language-tag">
                        {{ language.teachingLanguageName }}
                      </span>
                    </div>
                  </div>
                </ng-container>

                <!-- Vacation days -->
                <ng-container *ngSwitchCase="searchTeacherDtoFieldNames.vacationDates">
                  <div class="multiline"
                    [attr.style]="col.maxWidth !== undefined ? 'max-width:' + col.maxWidth + ' !important; width:max-content !important; ' : 'width:max-content !important;'">
                    <div class="vacation-days">
                      <span *ngFor="let day of teacher.vacationDates" class="vacation-day">
                        {{ day | date:'shortDate' }}
                      </span>
                    </div>
                  </div>
                </ng-container>

                <!-- Account status -->
                <ng-container *ngSwitchCase="searchTeacherDtoFieldNames.isBlocked">
                  <div class="multiline"
                    [attr.style]="col.maxWidth !== undefined ? 'max-width:' + col.maxWidth + ' !important; width:max-content !important; ' : 'width:max-content !important;'">
                    <span [ngClass]="teacher.isBlocked ? 'active-status' : 'inactive-status'">
                      {{ teacher.isBlocked ? 'Active' : 'Blocked' }}
                    </span>
                  </div>
                </ng-container>

                <ng-container *ngSwitchCase="searchTeacherDtoFieldNames.availabilityStatus">
                  <div class="multiline"
                    [attr.style]="col.maxWidth !== undefined ? 'max-width:' + col.maxWidth + ' !important; width:max-content !important; ' : 'width:max-content !important;'">
                    {{
                    this.enumDropdownOptionsService.getLabelFromValue(this.enumDropdownOptionsService.teacherAvailabilityStatusOptions,
                    teacher.availabilityStatus )}}
                  </div>
                </ng-container>


                <ng-container *ngSwitchCase="searchTeacherDtoFieldNames.lastAvailStatusUpdate">
                  <div class="multiline"
                    [attr.style]="col.maxWidth !== undefined ? 'max-width:' + col.maxWidth + ' !important; width:max-content !important; ' : 'width:max-content !important;'">
                    {{ formatUtcDateToAdminLocalized(teacher.lastAvailStatusUpdate) }}
                  </div>
                </ng-container>

                <ng-container *ngSwitchDefault>
                  <div class="multiline"
                    [attr.style]="col.maxWidth !== undefined ? 'max-width:' + col.maxWidth + ' !important; width:max-content !important; ' : 'width:max-content !important;'">
                    {{ teacher[col.field] }}
                  </div>
                </ng-container>
              </ng-container>
            </td>

          </tr>
        </ng-template>

        <!-- Table Footer -->
        <app-data-grid-header-footer
          [columns]="selectedColumns()"
          [sortable]="true"
          [reorderable]="true"
          [showHeader]="false"
          [showFooter]="true"
          [showActionsColumn]="true"
          actionsColumnHeader="Actions"
          actionsColumnWidth="100px">
        </app-data-grid-header-footer>

        <!-- Empty Message -->
        <ng-template pTemplate="emptymessage">
          <tr>
            <td colspan="15" class="text-center p-4">No teachers found.</td>
          </tr>
        </ng-template>
      </p-table>
    </div>
  </div>
</div>