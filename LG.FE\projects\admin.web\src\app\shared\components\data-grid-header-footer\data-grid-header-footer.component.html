<!-- Table Header -->
 <ng-container *ngIf="showHeader">

  <tr [class]="customRowClass">
    <!-- Actions Column (Frozen Left) -->
    <th
      *ngIf="showActionsColumn"
      alignFrozen="left"
      pFrozenColumn
      [style.width]="actionsColumnWidth">
      {{ actionsColumnHeader }}
    </th>

    <!-- Dynamic Columns - Reorderable -->
    <th
      *ngFor="let col of columns"
      pReorderableColumn
      [pSortableColumn]="isColumnSortable(col) ? col.field : undefined"
      [attr.style]="getColumnStyle(col)"
      [ngClass]="{ 'reorderable-column': isColumnReorderable() }">
      {{ col.header }}
      <p-sortIcon
        *ngIf="isColumnSortable(col)"
        [field]="col.field">
      </p-sortIcon>
    </th>
  </tr>
</ng-container>

<!-- Table Footer -->
<ng-template pTemplate="footer" *ngIf="showFooter">
  <tr [class]="customRowClass">
    <!-- Actions Column (Frozen Left) -->
    <th
      *ngIf="showActionsColumn"
      alignFrozen="left"
      pFrozenColumn
      [style.width]="actionsColumnWidth">
      {{ actionsColumnHeader }}
    </th>

    <!-- Dynamic Columns - Reorderable -->
    <th
      *ngFor="let col of columns"
      pReorderableColumn
      [pSortableColumn]="isColumnSortable(col) ? col.field : undefined"
      [attr.style]="getColumnStyle(col)"
      [ngClass]="{ 'reorderable-column': isColumnReorderable() }">
      {{ col.header }}
      <p-sortIcon
        *ngIf="isColumnSortable(col)"
        [field]="col.field">
      </p-sortIcon>
    </th>
  </tr>
</ng-template>
