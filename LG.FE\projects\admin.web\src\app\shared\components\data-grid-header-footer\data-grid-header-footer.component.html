<!-- Table Header -->
 <ng-container *ngIf="showHeader">

  <tr class="p-datatable-thead-row" [ngClass]="customRowClass">
    <!-- Actions Column (Frozen Left) -->
    <th
      *ngIf="showActionsColumn"
      class="p-datatable-thead-cell"
      alignFrozen="left"
      pFrozenColumn
      [style.width]="actionsColumnWidth">
      {{ actionsColumnHeader }}
    </th>

    <!-- Dynamic Columns - Reorderable -->
    <th
      *ngFor="let col of columns"
      class="p-datatable-thead-cell"
      pReorderableColumn
      [pSortableColumn]="isColumnSortable(col) ? col.field : undefined"
      [attr.style]="getColumnStyle(col)"
      [ngClass]="{ 'reorderable-column': isColumnReorderable() }">
      {{ col.header }}
      <p-sortIcon
        *ngIf="isColumnSortable(col)"
        [field]="col.field">
      </p-sortIcon>
    </th>
  </tr>
</ng-container>

<!-- Table Footer -->
<ng-template pTemplate="footer" *ngIf="showFooter">
  <tr class="p-datatable-tfoot-row" [ngClass]="customRowClass">
    <!-- Actions Column (Frozen Left) -->
    <th
      *ngIf="showActionsColumn"
      class="p-datatable-tfoot-cell"
      alignFrozen="left"
      pFrozenColumn
      [style.width]="actionsColumnWidth">
      {{ actionsColumnHeader }}
    </th>

    <!-- Dynamic Columns - Reorderable -->
    <th
      *ngFor="let col of columns"
      class="p-datatable-tfoot-cell"
      pReorderableColumn
      [pSortableColumn]="isColumnSortable(col) ? col.field : undefined"
      [attr.style]="getColumnStyle(col)"
      [ngClass]="{ 'reorderable-column': isColumnReorderable() }">
      {{ col.header }}
      <p-sortIcon
        *ngIf="isColumnSortable(col)"
        [field]="col.field">
      </p-sortIcon>
    </th>
  </tr>
</ng-template>
