<!-- Table Header -->
<ng-template pTemplate="header" *ngIf="showHeader">
  <tr [class]="customRowClass">
    <!-- Actions Column (Frozen Left) -->
    <th
      *ngIf="showActionsColumn"
      alignFrozen="left"
      pFrozenColumn
      [style.width]="actionsColumnWidth">
      {{ actionsColumnHeader }}
    </th>

    <!-- Dynamic Columns -->
    <th
      *ngFor="let col of columns"
      [pReorderableColumn]="isColumnReorderable() ? '' : null"
      [pSortableColumn]="isColumnSortable(col) ? col.field : undefined"
      [attr.style]="getColumnStyle(col)">
      {{ col.header }}
      <p-sortIcon
        *ngIf="isColumnSortable(col)"
        [field]="col.field">
      </p-sortIcon>
    </th>
  </tr>
</ng-template>

<!-- Table Footer -->
<ng-template pTemplate="footer" *ngIf="showFooter">
  <tr [class]="customRowClass">
    <!-- Actions Column (Frozen Left) -->
    <th
      *ngIf="showActionsColumn"
      alignFrozen="left"
      pFrozenColumn
      [style.width]="actionsColumnWidth">
      {{ actionsColumnHeader }}
    </th>

    <!-- Dynamic Columns -->
    <th
      *ngFor="let col of columns"
      [pReorderableColumn]="isColumnReorderable() ? '' : null"
      [pSortableColumn]="isColumnSortable(col) ? col.field : undefined"
      [attr.style]="getColumnStyle(col)">
      {{ col.header }}
      <p-sortIcon
        *ngIf="isColumnSortable(col)"
        [field]="col.field">
      </p-sortIcon>
    </th>
  </tr>
</ng-template>
