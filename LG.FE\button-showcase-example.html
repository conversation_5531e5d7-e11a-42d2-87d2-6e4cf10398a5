<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Standardized Button System Showcase</title>
    <style>
        /* Import the button styles - In real app, this would be included via Angular */
        :root {
            --deep-lavender: #604997;
            --deep-lavender-dark: #7260D2;
            --deep-lavender-rgb: 96, 73, 151;
            --aqua-marine: #43ddc4;
            --tiffany-blue: #85d6d6;
            --checkout-success: #0A855C;
            --pale-orange: #ffa560;
            --faded-orange: #ed995b;
            --peachy-pink: #fe8985;
            --pastel-red: #d86662;
            --soft-blue: #5191f7;
            --cool-blue: #399bc9;
            
            --btn-primary: var(--deep-lavender);
            --btn-primary-hover: var(--deep-lavender-dark);
            --btn-primary-rgb: var(--deep-lavender-rgb);
            --btn-secondary: var(--aqua-marine);
            --btn-secondary-hover: var(--tiffany-blue);
            --btn-success: var(--checkout-success);
            --btn-success-hover: #059669;
            --btn-warning: var(--pale-orange);
            --btn-warning-hover: var(--faded-orange);
            --btn-danger: var(--peachy-pink);
            --btn-danger-hover: var(--pastel-red);
            --btn-info: var(--soft-blue);
            --btn-info-hover: var(--cool-blue);
            
            --btn-border-radius: 8px;
            --btn-border-radius-sm: 6px;
            --btn-border-radius-lg: 12px;
            --btn-transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            --btn-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            --btn-shadow-hover: 0 4px 8px rgba(0, 0, 0, 0.15);
            --btn-shadow-focus: 0 0 0 3px rgba(var(--btn-primary-rgb), 0.2);
        }

        body {
            font-family: "Noto Sans", "Arial", sans-serif;
            background-color: #F2F5FF;
            padding: 2rem;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        h1, h2, h3 {
            color: var(--btn-primary);
            margin-bottom: 1rem;
        }

        .button-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .button-section {
            margin-bottom: 3rem;
        }

        .button-example {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .button-label {
            font-size: 0.875rem;
            font-weight: 600;
            color: #6b7280;
            margin-bottom: 0.5rem;
        }

        /* Include the standardized button styles */
        .btn-standard {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            padding: 0.75rem 1.25rem;
            border: 2px solid transparent;
            border-radius: var(--btn-border-radius);
            font-size: 0.875rem;
            font-weight: 600;
            line-height: 1;
            text-align: center;
            text-decoration: none;
            cursor: pointer;
            transition: var(--btn-transition);
            position: relative;
            overflow: hidden;
            box-shadow: var(--btn-shadow);
        }

        .btn-standard:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
            box-shadow: none !important;
        }

        .btn-standard:focus {
            outline: none;
            box-shadow: var(--btn-shadow-focus);
        }

        /* Size variants */
        .btn-sm {
            padding: 0.5rem 1rem;
            font-size: 0.8125rem;
            border-radius: var(--btn-border-radius-sm);
        }

        .btn-lg {
            padding: 1rem 1.75rem;
            font-size: 1rem;
            border-radius: var(--btn-border-radius-lg);
        }

        .btn-block {
            width: 100%;
            justify-content: center;
        }

        /* Color variants */
        .btn-primary {
            background-color: var(--btn-primary);
            border-color: var(--btn-primary);
            color: white;
        }

        .btn-primary:hover:not(:disabled) {
            background-color: var(--btn-primary-hover);
            border-color: var(--btn-primary-hover);
            transform: translateY(-1px);
            box-shadow: var(--btn-shadow-hover);
        }

        .btn-secondary {
            background-color: var(--btn-secondary);
            border-color: var(--btn-secondary);
            color: white;
        }

        .btn-secondary:hover:not(:disabled) {
            background-color: var(--btn-secondary-hover);
            border-color: var(--btn-secondary-hover);
            transform: translateY(-1px);
            box-shadow: var(--btn-shadow-hover);
        }

        .btn-success {
            background-color: var(--btn-success);
            border-color: var(--btn-success);
            color: white;
        }

        .btn-success:hover:not(:disabled) {
            background-color: var(--btn-success-hover);
            border-color: var(--btn-success-hover);
            transform: translateY(-1px);
            box-shadow: var(--btn-shadow-hover);
        }

        .btn-warning {
            background-color: var(--btn-warning);
            border-color: var(--btn-warning);
            color: white;
        }

        .btn-warning:hover:not(:disabled) {
            background-color: var(--btn-warning-hover);
            border-color: var(--btn-warning-hover);
            transform: translateY(-1px);
            box-shadow: var(--btn-shadow-hover);
        }

        .btn-danger {
            background-color: var(--btn-danger);
            border-color: var(--btn-danger);
            color: white;
        }

        .btn-danger:hover:not(:disabled) {
            background-color: var(--btn-danger-hover);
            border-color: var(--btn-danger-hover);
            transform: translateY(-1px);
            box-shadow: var(--btn-shadow-hover);
        }

        .btn-info {
            background-color: var(--btn-info);
            border-color: var(--btn-info);
            color: white;
        }

        .btn-info:hover:not(:disabled) {
            background-color: var(--btn-info-hover);
            border-color: var(--btn-info-hover);
            transform: translateY(-1px);
            box-shadow: var(--btn-shadow-hover);
        }

        /* Outlined variants */
        .btn-outline-primary {
            background-color: transparent;
            border-color: var(--btn-primary);
            color: var(--btn-primary);
        }

        .btn-outline-primary:hover:not(:disabled) {
            background-color: var(--btn-primary);
            border-color: var(--btn-primary);
            color: white;
            transform: translateY(-1px);
            box-shadow: var(--btn-shadow-hover);
        }

        .btn-outline-secondary {
            background-color: transparent;
            border-color: var(--btn-secondary);
            color: var(--btn-secondary);
        }

        .btn-outline-secondary:hover:not(:disabled) {
            background-color: var(--btn-secondary);
            border-color: var(--btn-secondary);
            color: white;
            transform: translateY(-1px);
            box-shadow: var(--btn-shadow-hover);
        }

        /* Soft variants */
        .btn-soft-primary-new {
            background-color: rgba(var(--btn-primary-rgb), 0.1);
            border-color: transparent;
            color: var(--btn-primary);
        }

        .btn-soft-primary-new:hover:not(:disabled) {
            background-color: rgba(var(--btn-primary-rgb), 0.15);
            color: var(--btn-primary-hover);
            transform: translateY(-1px);
            box-shadow: var(--btn-shadow-hover);
        }

        /* Ghost button */
        .btn-ghost {
            background-color: transparent;
            border-color: transparent;
            color: var(--btn-primary);
            box-shadow: none;
        }

        .btn-ghost:hover:not(:disabled) {
            background-color: rgba(var(--btn-primary-rgb), 0.05);
            color: var(--btn-primary-hover);
            transform: translateY(-1px);
        }

        /* CTA button */
        .btn-cta {
            background: linear-gradient(135deg, var(--btn-primary) 0%, var(--btn-primary-hover) 100%);
            border-color: var(--btn-primary);
            color: white;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .btn-cta:hover:not(:disabled) {
            background: linear-gradient(135deg, var(--btn-primary-hover) 0%, var(--btn-primary) 100%);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(var(--btn-primary-rgb), 0.3);
        }

        .code-example {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 1rem;
            margin: 1rem 0;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Standardized Button System Showcase</h1>
        <p>This showcase demonstrates the new standardized button system with modern, compact, minimal, and professional design following established UI/UX preferences.</p>

        <div class="button-section">
            <h2>Solid Button Variants</h2>
            <div class="button-grid">
                <div class="button-example">
                    <div class="button-label">Primary</div>
                    <button class="btn-standard btn-primary">Primary Action</button>
                </div>
                <div class="button-example">
                    <div class="button-label">Secondary</div>
                    <button class="btn-standard btn-secondary">Secondary Action</button>
                </div>
                <div class="button-example">
                    <div class="button-label">Success</div>
                    <button class="btn-standard btn-success">Success Action</button>
                </div>
                <div class="button-example">
                    <div class="button-label">Warning</div>
                    <button class="btn-standard btn-warning">Warning Action</button>
                </div>
                <div class="button-example">
                    <div class="button-label">Danger</div>
                    <button class="btn-standard btn-danger">Danger Action</button>
                </div>
                <div class="button-example">
                    <div class="button-label">Info</div>
                    <button class="btn-standard btn-info">Info Action</button>
                </div>
            </div>
        </div>

        <div class="button-section">
            <h2>Outlined Button Variants</h2>
            <div class="button-grid">
                <div class="button-example">
                    <div class="button-label">Outlined Primary</div>
                    <button class="btn-standard btn-outline-primary">Outlined Primary</button>
                </div>
                <div class="button-example">
                    <div class="button-label">Outlined Secondary</div>
                    <button class="btn-standard btn-outline-secondary">Outlined Secondary</button>
                </div>
            </div>
        </div>

        <div class="button-section">
            <h2>Button Sizes</h2>
            <div class="button-grid">
                <div class="button-example">
                    <div class="button-label">Small</div>
                    <button class="btn-standard btn-primary btn-sm">Small Button</button>
                </div>
                <div class="button-example">
                    <div class="button-label">Default</div>
                    <button class="btn-standard btn-primary">Default Button</button>
                </div>
                <div class="button-example">
                    <div class="button-label">Large</div>
                    <button class="btn-standard btn-primary btn-lg">Large Button</button>
                </div>
            </div>
        </div>

        <div class="button-section">
            <h2>Special Button Types</h2>
            <div class="button-grid">
                <div class="button-example">
                    <div class="button-label">Soft Primary</div>
                    <button class="btn-standard btn-soft-primary-new">Soft Primary</button>
                </div>
                <div class="button-example">
                    <div class="button-label">Ghost</div>
                    <button class="btn-standard btn-ghost">Ghost Button</button>
                </div>
                <div class="button-example">
                    <div class="button-label">CTA</div>
                    <button class="btn-standard btn-cta">Call to Action</button>
                </div>
                <div class="button-example">
                    <div class="button-label">Disabled</div>
                    <button class="btn-standard btn-primary" disabled>Disabled Button</button>
                </div>
            </div>
        </div>

        <div class="button-section">
            <h2>Full Width Button</h2>
            <div class="button-example">
                <div class="button-label">Block Button</div>
                <button class="btn-standard btn-primary btn-block">Full Width Button</button>
            </div>
        </div>

        <div class="button-section">
            <h2>Usage Examples</h2>
            <h3>HTML Examples</h3>
            <div class="code-example">
&lt;!-- Primary button --&gt;
&lt;button class="btn-standard btn-primary"&gt;Primary Action&lt;/button&gt;

&lt;!-- Secondary outlined button --&gt;
&lt;button class="btn-standard btn-outline-secondary"&gt;Secondary Action&lt;/button&gt;

&lt;!-- Small success button --&gt;
&lt;button class="btn-standard btn-success btn-sm"&gt;Small Success&lt;/button&gt;

&lt;!-- Full width danger button --&gt;
&lt;button class="btn-standard btn-danger btn-block"&gt;Full Width Danger&lt;/button&gt;
            </div>

            <h3>PrimeNG Integration</h3>
            <div class="code-example">
&lt;!-- PrimeNG button with standardized classes --&gt;
&lt;p-button styleClass="btn-standard btn-primary" label="Save" icon="pi pi-check"&gt;&lt;/p-button&gt;

&lt;!-- PrimeNG outlined button --&gt;
&lt;p-button styleClass="btn-standard btn-outline-secondary" label="Cancel" icon="pi pi-times"&gt;&lt;/p-button&gt;
            </div>
        </div>
    </div>
</body>
</html>
