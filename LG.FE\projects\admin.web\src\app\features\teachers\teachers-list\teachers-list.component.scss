// teachers-list.component.scss

:host {
  display: block;
  width: 100%;

  // Overall layout using inline-block instead of flex
  .teachers-page-container {
    width: 100%;
    max-width: 100%;

    // For mobile view, keep stacked layout
    .main-content,
    .sidebar {
      margin: 0;
      padding: 0;
      font-size: 14px;
      display: inline-block;
    }

    #filters-content-wrapper {
      transition: max-height 0.3s ease, opacity 0.3s ease;
      opacity: 1;

      #filters-content {
        border-top: 1px solid #bfbfbf;
        border-bottom: 1px solid #bfbfbf;
        overflow: visible;
        max-height: 75vh;
        overflow-y: auto;
        overflow-x: hidden;
        padding: 10px 5px 0 5px;
      }
    }

    .filter-toggle-btn {
      width: 32px !important; // Set fixed width
      height: 32px !important; // Set fixed height (same as width for perfect circle)
      border-radius: 50% !important; // Force complete circle
      display: flex !important; // Center the icon
      justify-content: center !important; // Horizontally center
      align-items: center !important; // Vertically center
      padding: 0 !important; // Remove padding
      transition: all 0.3s ease !important; // Smooth transition for all changes
      background-color: #ffffff !important; // White background
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important; // Subtle shadow

      &:hover {
        transform: scale(1.05) !important; // Slightly enlarge on hover
        box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15) !important; // Enhanced shadow on hover
      }

      .p-button-icon {
        font-size: 14px !important; // Adjust icon size
        margin: 0 !important; // Remove any margins
        color: #000B4A !important; // Match your primary color
      }

      &:focus {
        box-shadow: 0 0 0 2px rgba(96, 73, 151, 0.2) !important; // Custom focus ring
      }
    }

    .filter-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1rem;
      // This ensures the header stays visible when content collapses
      border-bottom: 1px solid transparent;

      button {
        transition: transform 0.3s ease;

        &:hover {
          background-color: rgba(255, 255, 255, 0.1);
        }
      }
    }


    // Order for mobile (reverse)
    .sidebar {
      border: 1px solid gray;
      border-radius: 7px;
      width: 350px;
      position: absolute;
      z-index: 1000;
      right: 5px;
      margin-bottom: 50px;
    }

    .main-content {
      width: 100%;
    }

    @media screen and (min-width: 992px) {
      // For desktop, use inline-block layout
      .main-content
      {
        display: inline-block;
      }

      .main-content {
        width: 100%; // Account for sidebar width + gap
      }
    }
  }

  // Applied filters styling
  // Applied filters styling
  .applied-filters-container {
    margin-bottom: 1rem;
    box-sizing: border-box;
    width: calc(100% - 360px); // Account for sidebar width + gap
    display: inline-block;
    font-size: 18px;
    color: #fff !important;


    .filter-chip {
      background-color: #5f4896 !important;
      border: 1px solid var(--surface-200);
      display: inline-block;
      margin: 0.25rem;
      padding: 5px !important;
      color: #fff !important;
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      user-select: none;

      span, i {
        font-size: 14px !important;
        color: #fff !important;
      }


      .p-button {
        color: #fff !important;
        width: 1.5rem;
        height: 1.5rem;
        padding: 0;
        border-radius: 50%;

        &:hover {
          background-color: #fff !important;
          color: var(--p-button-outlined-danger-color) !important;
        }
      }
    }
  }

  // Table styling - same effective approach


  .table-container {
    width: 100%;

    ::ng-deep {
      .p-datatable {
        width: 100%;

        // This is the key fix that's still needed
        table {
          display: inline-block;
          width: 1px; // Forces table to size to content
          min-width: 100%; // Ensures table is at least as wide as its container
          text-align: left;
        }

        // Basic row striping
        .p-datatable-tbody > tr:nth-child(odd) {
          background-color: rgba(0, 0, 0, 0.02);
        }

        /* Hover styles for all cells */
        .p-datatable-tbody > tr:hover > td {
          background-color: rgba(95, 72, 150, 0.05) !important;
        }

        .p-datatable-tbody > tr {
          height: auto; // Allow row to grow with content
        }

        .p-datatable-header {
          padding: 0px 0 8px 0px;
          margin: 0;
          background: transparent;
          border: 0;
        }

        .p-datatable-tbody > tr > td {
          white-space: normal !important; // Allow text to wrap
          padding: 8px 15px !important;
          height: auto !important;
          align-content: center;
        }

        .p-datatable-thead > tr > th,
        .p-datatable-tfoot > tr > th {
          background: #fff;
          color: #000;
          padding: 10px 15px;
          border: 0.8px solid rgb(226, 232, 240);
          border-right-width: 0px;
          border-collapse: separate;
        }

        .p-datatable-thead > tr > th {
          border-top: 0;
        }

        /* Key part: Custom styles for frozen column cells */
        td.p-frozen-column[style*="background-color"] {
          &:not(:hover) {
            &:nth-child(odd),
            tr:nth-child(odd) & {
              background-color: rgba(0, 0, 0, 0.02) !important;
            }

            &:nth-child(even),
            tr:nth-child(even) & {
              background-color: #ffffff !important;
            }
          }
        }
      }
    }
  }

  // Data display components inside the table
  .language-list {
    /* You could also convert this to inline-block if needed */
    display: inline-block;

    .language-tag {
      display: inline-block;
      padding: 0.2rem 0.5rem;
      background-color: var(--primary-color);
      color: var(--primary-color-text);
      border-radius: 1rem;
      white-space: nowrap;
      margin: 0.15rem;
      font-size: 12px;
    }
  }

  .vacation-days {
    display: inline-block;

    .vacation-day {
      display: inline-block;
      padding: 0.2rem 0.5rem;
      background-color: var(--gray-500);
      color: var(--white);
      border-radius: 1rem;
      white-space: nowrap;
      margin: 0.15rem;
      font-weight: 600;
      font-size: 12px;
    }
  }

  // Status indicators
  .active-status {
    color: var(--green-500);
    font-weight: 600;
  }

  .inactive-status {
    color: var(--red-500);
    font-weight: 600;
  }

  // Field checkbox alignment - can use inline-block too
  .field-checkbox {
    display: block;

    label {
      display: inline-block;
      margin-left: 0.5rem;
      vertical-align: middle;
    }
  }

  .multiline {
    min-height: 20px !important;
    word-break: break-word !important;
    word-wrap: break-word !important;
    hyphens: auto !important;
    max-width: 100%!important;
  }
  .search-input-container {
    position: relative;
    width: 100%;

    input {
      padding-right: 30px; /* Make room for the button */
      width: 100%;
    }

    .search-clear-button {
      position: absolute;
      right: 8px;
      top: 50%;
      transform: translateY(-50%);
      background: transparent;
      border: none;
      color: #6c757d;
      cursor: pointer;
      padding: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 2;

      &:hover {
        color: #000;
      }

      i {
        font-size: 14px;
      }
    }
  }

}


