// Import mixins for responsive design
@use 'mixins';

// Component-specific styles for data grid header and footer
:host {
  display: contents; // Allow the component to be transparent in the table structure
}

// Ensure proper styling for table headers and footers
// th {
//   // Maintain existing PrimeNG table styling
//   // Add any custom styling here if needed
  
//   // Responsive design using mixins
//   @include mobile {
//     // Mobile-specific header/footer styles
//     font-size: 0.875rem;
//     padding: 0.5rem;
//   }
  
//   @include tablet {
//     // Tablet-specific header/footer styles
//     font-size: 0.9rem;
//     padding: 0.75rem;
//   }
  
//   @include desktop {
//     // Desktop-specific header/footer styles
//     font-size: 1rem;
//     padding: 1rem;
//   }
// }

// Sort icon styling
.p-sortable-column .p-sortable-column-icon {
  // Ensure sort icons are properly positioned
  margin-left: 0.25rem;
}

// Frozen column styling
th[alignFrozen="left"] {
  // Ensure frozen columns maintain their styling
  position: sticky;
  left: 0;
  z-index: 1;
}
