// Import mixins for responsive design
@use 'mixins';

// Component-specific styles for data grid header and footer
:host {
  display: contents; // Allow the component to be transparent in the table structure
}

// Reorderable column styling - only component-specific behavior
.reorderable-column {
  cursor: move;

  &:hover {
    opacity: 0.8;
  }
}

// Non-reorderable columns
th:not(.reorderable-column) {
  cursor: default;
}
