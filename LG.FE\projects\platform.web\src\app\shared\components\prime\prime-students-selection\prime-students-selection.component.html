@if (selectionMode === 'single') {
<p-select #selectStudentsList [options]="items()" [ngModel]="selectedStudent()" [filter]="true"
    [autofocusFilter]="false" [filterFields]="['firstName','lastName']" placeholder="Select a Student from the list"
    class="w-full md:w-56" [styleClass]="styleClass()" appendTo="body" (onChange)="onSelectionChange($event)">

    <ng-template #empty>
        <div class="font-medium p-3">Available Students</div>
    </ng-template>

    <!-- Selected item -->
    <ng-template #selectedItem let-selectedOption>
        <ng-container *ngTemplateOutlet="commonTemplate; context: { data: selectedOption }"></ng-container>
    </ng-template>

    <!-- Dropdown item -->
    <ng-template let-country #item>
        <ng-container *ngTemplateOutlet="commonTemplate; context: { data: country }"></ng-container>
    </ng-template>

    <!-- Common reusable template -->
    <ng-template #commonTemplate let-data="data">
        <div class="flex align-items-center gap-2" *ngIf="data">
            @let studentProfile = data;
            <lib-prime-profile-photo-single [userId]="studentProfile.userId"
                [src]="studentProfile.profilePhotoUrl!"
                customClass="hidden sm:block w-3rem filter-brightness" [width]="42" [height]="42">
            </lib-prime-profile-photo-single>
            <div>
                <h3 class="flex align-items-start justify-content-start gap-1 m-0 text-sm sm:text-base">
                    <!-- {{studentProfile | json}} -->
                    <span>{{ studentProfile.firstName }} {{ studentProfile.lastName }}</span>

                    <span
                        class="font-light text-xs mt-1">{{this.generalService.calculateAge(studentProfile.dateOfBirth)}}
                        years
                        old</span>
                </h3>
                @if (data.studentTeachingLanguageDto! && data.studentTeachingLanguageDto!.length > 0) {
                <div class="flex align-items-center gap-2 mt-1 max-w-25rem">
                    <!-- Display first 3 languages as individual pills -->
                    <div *ngFor="let language of getVisibleLanguages(data.studentTeachingLanguageDto)"
                        class="inline-flex align-items-center gap-2 border-round-sm px-2 py-1
                        surface-ground surface-border border-1"
                        [pTooltip]="language.teachingLanguageName + ' - Level ' + generalService.getILanguageLevelsEnumText(language.languageLevel)">
                        <img [src]="generalService.getImageUrlForLanguage(language.teachingLanguageName)"
                            [alt]="language.teachingLanguageName" class="flag-size" />
                        <span class="text-xs">{{ language.teachingLanguageName }}</span>
                        <span class="text-xs font-medium border-round px-1" [ngClass]="{
                            'bg-blue-50 text-blue-600': language.languageLevel <= 2,
                            'bg-green-50 text-green-600': language.languageLevel > 2 && language.languageLevel <= 8,
                            'bg-orange-50 text-orange-600': language.languageLevel > 8
                        }">
                            {{ generalService.getILanguageLevelsEnumText(language.languageLevel) }}
                        </span>
                    </div>

                    <!-- Display remaining languages count if more than 3 -->
                    @if (shouldTruncateLanguages(data.studentTeachingLanguageDto)) {
                    <div class="inline-flex align-items-center gap-1 border-round-sm px-2 py-1
                        surface-100 text-600 border-1 border-300"
                        [pTooltip]="getRemainingLanguagesTooltip(data.studentTeachingLanguageDto)">
                        <i class="pi pi-plus text-xs"></i>
                        <span class="text-xs font-medium">{{ getRemainingLanguagesCount(data.studentTeachingLanguageDto) }} more</span>
                    </div>
                    }
                </div>
                }
                <div class="flex align-items-start text-xs mt-1" *ngIf="data.timezoneDisplayName">
                    {{ studentProfile.timezoneDisplayName }}
                </div>
             
            </div>
        </div>
    </ng-template>

</p-select>
}

@if (selectionMode === 'multiple') {

<p-multiselect #selectStudentsListMultiple class="w-full md:w-56" [appendTo]="'body'" [styleClass]="styleClass()" [(ngModel)]="selectedStudents"
    (onChange)="onSelectMultipleChange($event)"
    [filterBy]="'firstName,lastName'" [options]="items()" [filter]="true"
    placeholder="Select a Student from the list">

    <ng-template let-data pTemplate="item">

        @let studentProfile = data;

        <lib-prime-profile-photo-single [userId]="studentProfile.userId"
        customClass="hidden sm:block w-3rem filter-brightness" [width]="44" [height]="44">
    </lib-prime-profile-photo-single>
        
        <div>

            <h3 class="flex align-items-start justify-content-start gap-1 m-0 text-sm sm:text-sm">

                <span>{{ studentProfile.firstName }} {{ studentProfile.lastName }}</span>

                <span class="font-light text-xs mt-1">{{this.generalService.calculateAge(studentProfile.dateOfBirth)}}
                    years
                    old</span>
            </h3>
            @if (data.studentTeachingLanguageDto! && data.studentTeachingLanguageDto!.length > 0) {
                <div class="flex align-items-center gap-2 mt-1">
                    <!-- Display first 3 languages as individual pills -->
                    <div *ngFor="let language of getVisibleLanguages(data.studentTeachingLanguageDto)"
                        class="inline-flex align-items-center gap-2 border-round-sm px-2 py-1
                        surface-ground surface-border border-1"
                        [pTooltip]="language.teachingLanguageName + ' - Level ' + generalService.getILanguageLevelsEnumText(language.languageLevel)">
                        <img [src]="generalService.getImageUrlForLanguage(language.teachingLanguageName)"
                            [alt]="language.teachingLanguageName" class="flag-size" />
                        <span class="text-xs">{{ language.teachingLanguageName }}</span>
                        <span class="text-xs font-medium border-round px-1" [ngClass]="{
                            'bg-blue-50 text-blue-600': language.languageLevel <= 2,
                            'bg-green-50 text-green-600': language.languageLevel > 2 && language.languageLevel <= 8,
                            'bg-orange-50 text-orange-600': language.languageLevel > 8
                        }">
                            {{ generalService.getILanguageLevelsEnumText(language.languageLevel) }}
                        </span>
                    </div>

                    <!-- Display remaining languages count if more than 3 -->
                    @if (shouldTruncateLanguages(data.studentTeachingLanguageDto)) {
                    <div class="inline-flex align-items-center gap-1 border-round-sm px-2 py-1
                        surface-100 text-600 border-1 border-300"
                        [pTooltip]="getRemainingLanguagesTooltip(data.studentTeachingLanguageDto)">
                        <i class="pi pi-plus text-xs"></i>
                        <span class="text-xs font-medium">{{ getRemainingLanguagesCount(data.studentTeachingLanguageDto) }} more</span>
                    </div>
                    }
                </div>
                }
            <div class="flex align-items-start text-xs mt-1" *ngIf="studentProfile.timezoneDisplayName">
                {{ studentProfile.timezoneDisplayName }}
            </div>
            <!-- <span class="text-900 font-medium text-sm">
                @if (data.studentTeachingLanguageDto! && data.studentTeachingLanguageDto!.length > 0) {
                <div class="inline-flex align-items-center justify-content-center gap-2"
                    *ngFor="let language of data.studentTeachingLanguageDto!">
                    <img [src]="generalService.getImageUrlForLanguage(language.teachingLanguageName)" alt="Avatar"
                        class="flag-size ml-1" title="{{language.teachingLanguageName}}"
                        pTooltip="{{language.teachingLanguageName}}" tooltipPosition="top" />
                </div>
                }
            </span> -->
        </div>
    </ng-template>

    <ng-template let-selectedStudentSa pTemplate="selectedItems">
        <div class="flex flex-wrap">
            <ng-container *ngFor="let student of selectedStudentSa; let last = last">
                <span class="outline-1 border-gray-200 border-round pl-1">
                    {{ student.firstName }} {{ student.lastName }}</span>
                <span *ngIf="!last">,</span>
            </ng-container>
        </div>
    </ng-template>

    
    <ng-template #empty let-emptyMessage>
        <div class="font-medium">
            <div class="mt-1 flex flex-column md:flex-row align-items-center justify-content-between
            gap-3 px-3 py-2 border-round-md border-1 mb-3 bg-cyan-50 border-cyan-300 backdrop-blur-sm shadow-1">
               <div class="flex align-items-center gap-2">
                   <i class="pi pi-info-circle text-cyan-700 text-xl"></i>
                   <p class="text-cyan-700 text-sm m-0 line-height-3">
                       {{emptyMessageText}} 
                   </p>
               </div>
           </div>
        </div>
    </ng-template>
    <ng-template #emptyMessage>
        <div class="font-medium px-3 py-2">Test</div>
    </ng-template>

    <ng-template #filtericon>
        <i class="pi pi-search"></i>
    </ng-template>
    <ng-template #header>
        <div class="font-medium px-3 py-2">Available Students</div>
    </ng-template>
    <ng-template #footer>
        <div class="p-3 flex justify-content-between align-items-center gap-2">
            <div class="flex gap-2">
                <p-button label="Remove All" 
                         severity="danger" 
                         text 
                         size="small" 
                         icon="pi pi-times" 
                         (onClick)="clearAllStudents()"/>
                <p-button label="Select All" 
                         severity="secondary" 
                         text 
                         size="small" 
                         icon="pi pi-check-circle"
                         (onClick)="selectAllStudents()"/>
            </div>
            <div class="flex gap-2">
                <p-button label="Done" 
                         severity="primary"
                         size="small"
                         icon="pi pi-check"
                         (onClick)="selectStudentsListMultiple.close($event)" />
            </div>
        </div>
    </ng-template>
</p-multiselect>
}