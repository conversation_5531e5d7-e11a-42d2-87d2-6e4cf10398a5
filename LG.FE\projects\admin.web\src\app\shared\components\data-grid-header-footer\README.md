# DataGridHeaderFooterComponent

A reusable Angular component for generating table headers and footers in data grid implementations across the admin web application.

## Features

- **Dynamic Column Generation**: Automatically generates table headers and footers based on column definitions
- **PrimeNG Integration**: Full compatibility with PrimeNG table sorting and column reordering
- **Frozen Columns**: Support for frozen action columns
- **Responsive Design**: Uses mixins from shared-modules.library for responsive behavior
- **Type Safety**: Full TypeScript support with `IDataGridFields` interface
- **Flexible Configuration**: Multiple input properties for customization
- **Conditional Reordering**: Visual feedback for reorderable vs non-reorderable columns

## Input Properties

| Property | Type | Default | Description |
|----------|------|---------|-------------|
| `columns` | `IDataGridFields[]` | `[]` | Array of column definitions |
| `showHeader` | `boolean` | `true` | Toggle header visibility |
| `showFooter` | `boolean` | `true` | Toggle footer visibility |
| `sortable` | `boolean` | `true` | Enable/disable sorting functionality |
| `reorderable` | `boolean` | `true` | Enable/disable column reordering |
| `showActionsColumn` | `boolean` | `true` | Show frozen actions column |
| `actionsColumnHeader` | `string` | `'Actions'` | Actions column header text |
| `actionsColumnWidth` | `string` | `'100px'` | Actions column width |
| `customRowClass` | `string` | `''` | Custom CSS classes for rows |

## Usage Examples

### Basic Usage
```html
<app-data-grid-header-footer 
  [columns]="selectedColumns()" 
  [sortable]="true">
</app-data-grid-header-footer>
```

### Header Only
```html
<app-data-grid-header-footer 
  [columns]="columns" 
  [showHeader]="true"
  [showFooter]="false"
  [sortable]="true"
  [reorderable]="true">
</app-data-grid-header-footer>
```

### Footer Only
```html
<app-data-grid-header-footer 
  [columns]="columns" 
  [showHeader]="false"
  [showFooter]="true"
  [sortable]="false">
</app-data-grid-header-footer>
```

### Custom Actions Column
```html
<app-data-grid-header-footer 
  [columns]="columns" 
  [showActionsColumn]="true"
  actionsColumnHeader="Operations"
  actionsColumnWidth="150px">
</app-data-grid-header-footer>
```

### No Actions Column
```html
<app-data-grid-header-footer 
  [columns]="columns" 
  [showActionsColumn]="false">
</app-data-grid-header-footer>
```

## Integration with PrimeNG Table

The component is designed to work seamlessly with PrimeNG tables:

```html
<p-table [value]="data" [columns]="selectedColumns()">
  <!-- Use the component for header -->
  <app-data-grid-header-footer 
    [columns]="selectedColumns()" 
    [showHeader]="true"
    [showFooter]="false">
  </app-data-grid-header-footer>
  
  <!-- Table body -->
  <ng-template pTemplate="body" let-item>
    <!-- Your table body content -->
  </ng-template>
  
  <!-- Use the component for footer -->
  <app-data-grid-header-footer 
    [columns]="selectedColumns()" 
    [showHeader]="false"
    [showFooter]="true">
  </app-data-grid-header-footer>
</p-table>
```

## Column Definition

The component expects columns to follow the `IDataGridFields` interface:

```typescript
interface IDataGridFields {
  field: string;
  header: string;
  sortable?: boolean;
  maxWidth?: string;
}
```

Example:
```typescript
const columns: IDataGridFields[] = [
  { field: 'firstName', header: 'First Name', sortable: true },
  { field: 'lastName', header: 'Last Name', sortable: true },
  { field: 'email', header: 'Email', sortable: true },
  { field: 'country', header: 'Country', sortable: true, maxWidth: '200px' }
];
```

## Styling

The component uses responsive mixins from `shared-modules.library`:
- Mobile: Smaller font size and padding
- Tablet: Medium font size and padding  
- Desktop: Full font size and padding

Custom styling can be applied using the `customRowClass` input property.

## File Structure

```
data-grid-header-footer/
├── data-grid-header-footer.component.ts
├── data-grid-header-footer.component.html
├── data-grid-header-footer.component.scss
├── index.ts
└── README.md
```

## Import

```typescript
import { DataGridHeaderFooterComponent } from '../../../shared/components/data-grid-header-footer';
```
