// teacher-list-component-helper.service.ts
import { Injectable } from '@angular/core';
import { Table } from 'primeng/table';
import { ISearchTeacherDto } from 'SharedModules.Library';
import {inject} from '@angular/core';

import {
  IGetTeachersResponse,
  EnumDropdownOptionsService,
  IDataGridFields,
  nameOf,
  IGetTeachersRequest,
  IGenderEnum,
} from "SharedModules.Library";
import moment from 'moment-timezone';
import {Params} from "@angular/router";


@Injectable({
  providedIn: 'root'
})
export class TeacherListComponentHelperService {
   enumDropdownOptionsService = inject(EnumDropdownOptionsService) ;
  constructor() {
  }

  exportTeacherTable(table: Table, cols: IDataGridFields[], teachersResponse: IGetTeachersResponse) {
    if (!table || !teachersResponse.pageData || teachersResponse.pageData.length === 0) {
      return;
    }

    // Create a completely new object with string values for all properties
    const formattedData = teachersResponse.pageData.map(teacher => {
      // Create a new export object with all string values
      const exportObject: Record<string, string> = {};
      const teacherFields = nameOf<ISearchTeacherDto>();

      // Process each property explicitly based on field names from cols
      for (const col of cols) {
        const fieldName = col.field;

        if (fieldName === teacherFields.firstName) {
          exportObject[fieldName] = teacher.firstName || '';
        }

        else if (fieldName === teacherFields.lastName) {
          exportObject[fieldName] = teacher.lastName || '';
        }

        else if (fieldName === teacherFields.primaryEmail) {
          exportObject[fieldName] = teacher.primaryEmail || '';
        }

        else if (fieldName === teacherFields.gender) {
          exportObject[fieldName] = teacher.gender === 'Male' ? 'Male' : 'Female';
        }

        else if (fieldName === teacherFields.country) {
          exportObject[fieldName] = teacher.country || '';
        }

        else if (fieldName === teacherFields.isBlocked) {
          exportObject[fieldName] = teacher.isBlocked ? 'Blocked' : 'Active';
        }

        // else if (fieldName === teacherFields.dateOfRegistration) {
        //   if (teacher.dateOfRegistration) {
        //     exportObject[fieldName] = new Date(teacher.dateOfRegistration).toLocaleDateString();
        //   } else {
        //     exportObject[fieldName] = '';
        //   }
        // }

        else if (fieldName === teacherFields.availabilityStatus) {
          if (teacher.availabilityStatus !== undefined) {
            var availabilityOptions = this.enumDropdownOptionsService.teacherAvailabilityStatusOptions;
            var availabilityValue = this.enumDropdownOptionsService.getLabelFromValue(availabilityOptions, teacher.availabilityStatus);
            exportObject[fieldName] = availabilityValue;
          } else {
            exportObject[fieldName] = '';
          }
        }

        else if (fieldName === teacherFields.lastAvailStatusUpdate) {
          if (teacher.lastAvailStatusUpdate) {
            exportObject[fieldName] = new Date(teacher.lastAvailStatusUpdate).toLocaleDateString();
          } else {
            exportObject[fieldName] = '';
          }
        }

        else if (fieldName === teacherFields.speakingLanguages) {
          if (Array.isArray(teacher.speakingLanguages)) {
            if (teacher.speakingLanguages.length > 0 &&
              typeof teacher.speakingLanguages[0] === 'object' &&
              teacher.speakingLanguages[0] !== null &&
              'name' in teacher.speakingLanguages[0]) {
              exportObject[fieldName] = teacher.speakingLanguages
                .map((item: any) => item.name)
                .join(', ');
            } else {
              exportObject[fieldName] = teacher.speakingLanguages.join(', ');
            }
          } else {
            exportObject[fieldName] = '';
          }
        }

        else if (fieldName === teacherFields.teacherTeachingLanguages) {
          if (Array.isArray(teacher.teacherTeachingLanguages)) {
            if (teacher.teacherTeachingLanguages.length > 0 &&
              typeof teacher.teacherTeachingLanguages[0] === 'object' &&
              teacher.teacherTeachingLanguages[0] !== null &&
              'name' in teacher.teacherTeachingLanguages[0]) {
              exportObject[fieldName] = teacher.teacherTeachingLanguages
                .map((item: any) => item.name)
                .join(', ');
            } else {
              exportObject[fieldName] = teacher.teacherTeachingLanguages.join(', ');
            }
          } else {
            exportObject[fieldName] = '';
          }
        }

        else if (fieldName === teacherFields.vacationDates) {
          if (Array.isArray(teacher.vacationDates)) {
            if (teacher.vacationDates.length > 0 && typeof teacher.vacationDates[0] === 'string') {
              exportObject[fieldName] = teacher.vacationDates
                .map(date => new Date(date).toLocaleDateString())
                .join(', ');
            } else {
              exportObject[fieldName] = teacher.vacationDates.join(', ');
            }
          } else {
            exportObject[fieldName] = '';
          }
        }

        else if (fieldName === teacherFields.activePackagesCount) {
          exportObject[fieldName] = teacher.activePackagesCount?.toString() || '0';
        }

        else if (fieldName === teacherFields.activeStudentsCount) {
          exportObject[fieldName] = teacher.activeStudentsCount?.toString() || '0';
        }

        else {
          // For any other fields, fall back to simple string conversion
          const value = teacher[fieldName as keyof typeof teacher];
          exportObject[fieldName] = value !== undefined && value !== null ? String(value) : '';
        }
      }

      return exportObject;
    });

    // Generate CSV and trigger download using the existing methods
    const csvContent = this.convertToCSV(formattedData, cols);
    const formattedDate = moment().format('YYYY-MM-DD_HH-mm-ss');
    this.downloadCSV(csvContent, `teachers_export_${formattedDate}.csv`);
  }

  private convertToCSV(data: Record<string, string>[], cols:IDataGridFields[]): string {
    if (data.length === 0) {
      return '';
    }

    // Get headers from column definitions
    const headers = cols.map(col => col.header);
    const fields = cols.map(col => col.field);

    // Create CSV header row
    let csvString = headers.join(',') + '\r\n';

    // Add data rows
    for (const item of data) {
      const values = fields.map(field => {
        // Handle values with commas by wrapping in quotes
        const value = item[field] || '';
        return value.includes(',') ? `"${value}"` : value;
      });
      csvString += values.join(',') + '\r\n';
    }

    return csvString;
  }

  private downloadCSV(csvContent: string, filename: string): void {
    const blob = new Blob([csvContent], {
      type: 'text/csv;charset=utf-8;'
    });

    // Create download link and trigger it
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = filename;
    link.style.display = 'none';
    document.body.appendChild(link);
    link.click();

    // Clean up
    setTimeout(() => {
      document.body.removeChild(link);
      URL.revokeObjectURL(link.href);
    }, 100);
  }

  /**
   * Creates a default IGetTeachersRequest object with standard values
   * @param searchTeacherDtoFieldNames Optional object containing field names to customize the default sortColumn
   * @returns IGetTeachersRequest with default values
   */
  createDefaultTeachersRequest(searchTeacherDtoFieldNames?: any): IGetTeachersRequest {
    return {
      pageNumber: 1,
      pageSize: 10,
      sortColumn: searchTeacherDtoFieldNames?.firstName || 'firstName',
      sortDirection: 'asc',
      searchTerm: null,
      gender: IGenderEnum.None,
      approvedDateFrom: null,
      approvedDateTo: null,
      teachingLanguage: null,
      speakingLanguage: null,
      includeBlocked: null,
      availabilityStatus: null,
      teachingAgesExperience: null,
      teacherStudentAgesPreference: null,
      studentAgesMin: 2,
      studentAgesMax: 17,
    };
  }

  /**
   * Maps URL query parameters to an IGetTeachersRequest object
   * @param params Route parameters from ActivatedRoute
   * @param defaultRequest Optional default request to use as base (will create one if not provided)
   * @returns IGetTeachersRequest populated with values from URL parameters
   */
  mapQueryParamsToTeachersRequest(params: Params, defaultRequest?: IGetTeachersRequest): IGetTeachersRequest {
    // Start with clean defaults or use provided default
    const request: IGetTeachersRequest = defaultRequest || this.createDefaultTeachersRequest();

    // Use nameOf to get type-safe property names
    const paramsMap = nameOf<IGetTeachersRequest>();

    // Dynamically map query params from `paramsMap`
    if (params[paramsMap.pageNumber] !== undefined) {
      request.pageNumber = +params[paramsMap.pageNumber];
    }
    if (params[paramsMap.pageSize] !== undefined) {
      request.pageSize = +params[paramsMap.pageSize];
    }
    if (params[paramsMap.sortColumn!] !== undefined && params[paramsMap.sortColumn!] !== 'null') {
      request.sortColumn = params[paramsMap.sortColumn!];
    }
    if (params[paramsMap.sortDirection!] !== undefined && params[paramsMap.sortDirection!] !== 'null') {
      request.sortDirection = params[paramsMap.sortDirection!] as 'asc' | 'desc';
    }
    if (params[paramsMap.searchTerm!] !== undefined && params[paramsMap.searchTerm!] !== 'null') {
      request.searchTerm = params[paramsMap.searchTerm!];
    }
    if (params[paramsMap.gender] !== undefined) {
      request.gender = +params[paramsMap.gender];
    }
    if (params[paramsMap.approvedDateFrom!] !== undefined && params[paramsMap.approvedDateFrom!] !== 'null') {
      request.approvedDateFrom = new Date(params[paramsMap.approvedDateFrom!]);
    }
    if (params[paramsMap.approvedDateTo!] !== undefined && params[paramsMap.approvedDateTo!] !== 'null') {
      request.approvedDateTo = new Date(params[paramsMap.approvedDateTo!]);
    }
    if (params[paramsMap.teachingLanguage!] !== undefined && params[paramsMap.teachingLanguage!] !== 'null') {
      request.teachingLanguage = params[paramsMap.teachingLanguage!];
    }
    if (params[paramsMap.speakingLanguage!] !== undefined && params[paramsMap.speakingLanguage!] !== 'null') {
      request.speakingLanguage = params[paramsMap.speakingLanguage!];
    }
    if (params[paramsMap.includeBlocked!] !== undefined) {
      request.includeBlocked = params[paramsMap.includeBlocked!] === 'true';
    }
    if (params[paramsMap.availabilityStatus!] !== undefined && params[paramsMap.availabilityStatus!] !== 'null') {
      request.availabilityStatus = +params[paramsMap.availabilityStatus!];
    }
    if (params[paramsMap.teachingAgesExperience!] !== undefined && params[paramsMap.teachingAgesExperience!] !== 'null') {
      request.teachingAgesExperience = +params[paramsMap.teachingAgesExperience!];
    }
    if (params[paramsMap.teacherStudentAgesPreference!] !== undefined && params[paramsMap.teacherStudentAgesPreference!] !== 'null') {
      request.teacherStudentAgesPreference = +params[paramsMap.teacherStudentAgesPreference!];
    }
    if (params[paramsMap.studentAgesMin] !== undefined) {
      request.studentAgesMin = +params[paramsMap.studentAgesMin];
    }
    if (params[paramsMap.studentAgesMax] !== undefined) {
      request.studentAgesMax = +params[paramsMap.studentAgesMax];
    }

    return request;
  }

  initializeTableColumns(): IDataGridFields[] {
   const searchTeacherDtoFieldNames = nameOf<ISearchTeacherDto>();

    return [
      {field: searchTeacherDtoFieldNames.firstName, header: 'First Name', sortable: true},
      {field: searchTeacherDtoFieldNames.lastName, header: 'Last Name', sortable: true},
      {field: searchTeacherDtoFieldNames.primaryEmail, header: 'Email', sortable: true},
      {field: searchTeacherDtoFieldNames.gender, header: 'Gender', sortable: true},
      {field: searchTeacherDtoFieldNames.country, header: 'Country', sortable: true, maxWidth:"200px"},
      {field: searchTeacherDtoFieldNames.speakingLanguages, header: 'Native Speaking Languages', sortable: false},
      // {field: searchTeacherDtoFieldNames.dateOfRegistration!, header: 'Registration Date', sortable: true},
      {field: searchTeacherDtoFieldNames.activePackagesCount, header: 'Active Packages', sortable: true},
      {field: searchTeacherDtoFieldNames.activeStudentsCount, header: 'Active Students', sortable: true},
      {field: searchTeacherDtoFieldNames.vacationDates, header: 'Vacation Days', sortable: false, maxWidth:"300px"},
      {field: searchTeacherDtoFieldNames.teacherTeachingLanguages, header: 'Teaching Languages', sortable: false},
      {field: searchTeacherDtoFieldNames.availabilityStatus, header: 'Availability Status', sortable: true},
      {field: searchTeacherDtoFieldNames.lastAccountStatusUpdate!, header: 'Last Acc Status Update', sortable: true},
      {field: searchTeacherDtoFieldNames.accountStatus, header: 'Account Status', sortable: true}
    ];
  }
}
